'use client';

import { useState, useEffect } from 'react';
import { Brain, Cog, FileText, Zap, CheckCircle } from 'lucide-react';
import { cn } from '@/utils/cn';

interface ThinkingStep {
  id: string;
  icon: React.ReactNode;
  text: string;
  status: 'pending' | 'active' | 'completed';
  duration?: number;
}

interface ThinkingIndicatorProps {
  isVisible: boolean;
  currentStep?: string;
  steps?: ThinkingStep[];
  className?: string;
}

const defaultSteps: ThinkingStep[] = [
  {
    id: 'analyzing',
    icon: <Brain className="w-4 h-4" />,
    text: '分析您的请求...',
    status: 'pending',
    duration: 2000
  },
  {
    id: 'processing',
    icon: <Cog className="w-4 h-4" />,
    text: '处理代码逻辑...',
    status: 'pending',
    duration: 3000
  },
  {
    id: 'generating',
    icon: <FileText className="w-4 h-4" />,
    text: '生成解决方案...',
    status: 'pending',
    duration: 2500
  },
  {
    id: 'optimizing',
    icon: <Zap className="w-4 h-4" />,
    text: '优化代码结构...',
    status: 'pending',
    duration: 2000
  }
];

function TypingDots() {
  return (
    <div className="flex space-x-1">
      <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce [animation-delay:-0.3s]"></div>
      <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce [animation-delay:-0.15s]"></div>
      <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce"></div>
    </div>
  );
}

function PulsingBrain() {
  return (
    <div className="relative">
      <div className="absolute inset-0 bg-blue-500 rounded-full animate-ping opacity-20"></div>
      <div className="relative bg-gradient-to-r from-blue-500 to-purple-600 rounded-full p-3">
        <Brain className="w-6 h-6 text-white" />
      </div>
    </div>
  );
}

function StepIndicator({ step, isActive }: { step: ThinkingStep; isActive: boolean }) {
  return (
    <div className={cn(
      "flex items-center space-x-3 p-3 rounded-lg transition-all duration-500",
      isActive ? "bg-blue-50 border-l-4 border-blue-500" : "bg-gray-50",
      step.status === 'completed' && "bg-green-50 border-l-4 border-green-500"
    )}>
      <div className={cn(
        "flex-shrink-0 transition-all duration-300",
        isActive && "text-blue-600 scale-110",
        step.status === 'completed' && "text-green-600"
      )}>
        {step.status === 'completed' ? (
          <CheckCircle className="w-4 h-4" />
        ) : (
          step.icon
        )}
      </div>
      
      <div className="flex-1">
        <p className={cn(
          "text-sm font-medium transition-colors duration-300",
          isActive ? "text-blue-900" : "text-gray-700",
          step.status === 'completed' && "text-green-900"
        )}>
          {step.text}
        </p>
      </div>
      
      {isActive && step.status !== 'completed' && (
        <div className="flex-shrink-0">
          <TypingDots />
        </div>
      )}
    </div>
  );
}

export function ThinkingIndicator({ 
  isVisible, 
  currentStep, 
  steps = defaultSteps, 
  className 
}: ThinkingIndicatorProps) {
  const [activeStepIndex, setActiveStepIndex] = useState(0);
  const [localSteps, setLocalSteps] = useState(steps);

  useEffect(() => {
    if (!isVisible) {
      setActiveStepIndex(0);
      setLocalSteps(steps.map(step => ({ ...step, status: 'pending' as const })));
      return;
    }

    const interval = setInterval(() => {
      setLocalSteps(prevSteps => {
        const newSteps = [...prevSteps];
        
        // 标记当前步骤为完成
        if (activeStepIndex > 0) {
          newSteps[activeStepIndex - 1].status = 'completed';
        }
        
        // 激活当前步骤
        if (activeStepIndex < newSteps.length) {
          newSteps[activeStepIndex].status = 'active';
        }
        
        return newSteps;
      });

      setActiveStepIndex(prev => {
        if (prev < steps.length - 1) {
          return prev + 1;
        }
        return prev;
      });
    }, 2500);

    return () => clearInterval(interval);
  }, [isVisible, steps, activeStepIndex]);

  if (!isVisible) return null;

  return (
    <div className={cn(
      "bg-white/90 backdrop-blur-sm rounded-2xl shadow-lg border border-white/50 p-6 max-w-md mx-auto animate-slide-up",
      className
    )}>
      {/* 头部 */}
      <div className="flex items-center space-x-4 mb-6">
        <PulsingBrain />
        <div>
          <h3 className="text-lg font-semibold text-gray-900">AI 正在思考</h3>
          <p className="text-sm text-gray-600">请稍候，正在为您处理...</p>
        </div>
      </div>

      {/* 步骤列表 */}
      <div className="space-y-2">
        {localSteps.map((step, index) => (
          <StepIndicator 
            key={step.id} 
            step={step} 
            isActive={index === activeStepIndex && step.status === 'active'} 
          />
        ))}
      </div>

      {/* 底部提示 */}
      <div className="mt-6 pt-4 border-t border-gray-100">
        <p className="text-xs text-gray-500 text-center">
          💡 AI正在调用多个工具来为您提供最佳解决方案
        </p>
      </div>
    </div>
  );
}
